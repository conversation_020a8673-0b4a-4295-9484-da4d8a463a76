# Authentication Setup Guide

## Prerequisites

1. **Install Firebase dependencies**:
   ```bash
   npm install firebase@^10.13.0
   ```

2. **Firebase Project Setup**:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or use existing one
   - Enable Authentication → Sign-in method → Email/Password
   - Go to Project Settings → General → Your apps → Add app (Web)
   - Copy the Firebase config object

## Configuration

### 1. Frontend Firebase Config

Update `/services/firebase.ts` with your actual Firebase configuration:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "*********",
  appId: "your-actual-app-id"
};
```

### 2. Backend Firebase Admin SDK

The backend already has Firebase Admin SDK configured. Ensure your service account key is properly placed and referenced in your environment.

## Authentication Flow

1. **User Registration/Login**: 
   - Users can create accounts or sign in with email/password
   - Firebase handles authentication and returns ID tokens

2. **Backend Verification**:
   - Frontend sends ID token to `/auth` endpoint
   - Backend verifies token with Firebase Admin SDK
   - Returns user info if valid

3. **API Authentication**:
   - All API calls use `fetchWithAuth()` function
   - Automatically includes Bearer token in Authorization header
   - Validates security headers for critical endpoints

## Usage

### Sign In/Up
```typescript
import { signIn, signUp } from '../services/firebase';

// Sign up
const userCredential = await signUp(email, password);

// Sign in  
const userCredential = await signIn(email, password);
```

### Making Authenticated API Calls
```typescript
import { fetchWithAuth } from '../services/auth';

// All API calls automatically include authentication
const response = await fetchWithAuth('/api/tasks', {
  method: 'POST',
  body: JSON.stringify(taskData)
});
```

### Using Auth Context
```typescript
import { useAuth } from '../contexts/AuthContext';

function MyComponent() {
  const { user, loading, signOut } = useAuth();
  
  if (loading) return <LoadingScreen />;
  if (!user) return <SignInScreen />;
  
  return <AuthenticatedApp />;
}
```

## Security Features

- **Token Validation**: All API requests include Firebase ID tokens
- **Security Headers**: Critical endpoints validate security headers
- **Timestamp Validation**: Prevents replay attacks with timestamp checking
- **Nonce Validation**: Uses unique nonces to prevent request reuse
- **CORS Protection**: Configured for specific allowed origins

## Testing

1. Start the backend server:
   ```bash
   cd todolist-api
   python app.py
   ```

2. Start the frontend:
   ```bash
   cd baam-frontend
   npm start
   ```

3. Test the flow:
   - App should show sign-in screen initially
   - Create a new account or sign in
   - Should redirect to main app after successful authentication
   - All API calls should work with proper authentication

## Troubleshooting

- **"No user logged in"**: Check Firebase config and network connectivity
- **"Backend authentication failed"**: Verify backend is running and Firebase Admin SDK is configured
- **CORS errors**: Ensure frontend URL is in backend's allowed origins
- **Security header errors**: Check that backend is sending required security headers
