import { Platform } from 'react-native';
import Constants from 'expo-constants';

/**
 * Get the appropriate API base URL for the current platform
 * This handles the difference between web (localhost) and mobile (network IP)
 */
export const getApiBaseUrl = (): string => {
  if (Platform.OS === 'web') {
    return 'http://localhost:5001';
  } else {
    // For mobile devices, we need to use the computer's IP address
    // Expo provides this through Constants.expoConfig.hostUri
    const hostUri = Constants.expoConfig?.hostUri;
    
    if (hostUri) {
      // Extract IP from hostUri (format: "*************:8081")
      const ip = hostUri.split(':')[0];
      return `http://${ip}:5001`;
    }
    
    // Fallback to a common local network IP
    // You may need to update this to match your network
    return 'http://*************:5001';
  }
};

export const API_BASE_URL = getApiBaseUrl();

// Debug logging to help troubleshoot connection issues
if (__DEV__) {
  console.log(`[API Config] Platform: ${Platform.OS}`);
  console.log(`[API Config] API Base URL: ${API_BASE_URL}`);
  
  if (Platform.OS !== 'web') {
    console.log(`[API Config] Expo Host URI: ${Constants.expoConfig?.hostUri}`);
  }
}
