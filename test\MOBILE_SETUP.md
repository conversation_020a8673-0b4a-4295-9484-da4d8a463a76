# Mobile App Setup Guide

This guide helps fix common issues when running the BAAM app on mobile devices.

## Issues Fixed

### 1. Placeholder Text Not Showing
- **Problem**: Placeholder text in sign-up form inputs was not visible on mobile devices
- **Solution**: Added explicit `placeholderTextColor="#666666"` to all TextInput components
- **Additional**: Added explicit text color and border styling for better visibility

### 2. "Could not check handle" Error
- **Problem**: Mobile app couldn't connect to backend server at `localhost:5001`
- **Solution**: Created dynamic API configuration that uses the correct IP address for mobile devices

## How It Works

### API Configuration
The app now automatically detects the correct API base URL:
- **Web**: Uses `http://localhost:5001`
- **Mobile**: Uses your computer's IP address (e.g., `http://*************:5001`)

### Backend CORS Configuration
The backend now accepts requests from:
- Localhost (for web development)
- Local network IPs (for mobile development)
- Specific allowed origins

## Setup Instructions

### 1. Start the Backend Server
```bash
cd baam
python app.py
```
The server should start on `http://localhost:5001`

### 2. Start the Mobile App
```bash
cd test
npm start
```

### 3. Connect Your Phone
1. Make sure your phone and computer are on the same WiFi network
2. Scan the QR code with Expo Go app
3. The app will automatically use the correct IP address

### 4. Verify Connection
The app will automatically check backend connectivity when you open the sign-in screen. Check the console logs for any connection issues.

## Troubleshooting

### If Handle Checking Still Fails

1. **Check Network Connection**
   - Ensure phone and computer are on same WiFi
   - Try accessing `http://YOUR_COMPUTER_IP:5001` in phone browser

2. **Update IP Address**
   - If your computer's IP changed, update `test/config/api.ts`
   - Or restart the Expo dev server to get the new IP

3. **Check Backend Server**
   - Verify backend is running: `curl http://localhost:5001/`
   - Check for any error messages in backend console

4. **Check Firewall**
   - Ensure Windows Firewall allows connections on port 5001
   - Try temporarily disabling firewall to test

### If Placeholder Text Still Not Visible

1. **Check Device Theme**
   - Some devices may override placeholder colors
   - Try changing `placeholderTextColor` to a more contrasting color

2. **Check React Native Version**
   - Update to latest compatible versions if needed

## Debug Information

The app now logs helpful debug information:
- API base URL being used
- Backend health check results
- Handle checking attempts and results

Check the Expo console or device logs for this information.

## Files Modified

- `test/config/api.ts` - Dynamic API configuration
- `test/services/api.ts` - Updated to use new config
- `test/services/auth.ts` - Updated to use new config
- `test/components/SignInScreen.tsx` - Fixed placeholder colors and added debugging
- `test/utils/healthCheck.ts` - Backend connectivity testing
- `baam/app.py` - Updated CORS configuration for mobile support
