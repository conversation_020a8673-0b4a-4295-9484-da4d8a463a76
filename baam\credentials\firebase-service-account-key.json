{"type": "service_account", "project_id": "baam-7e29b", "private_key_id": "d1fd7d474d7d9dd8e8c57954c9c084b85769df19", "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDSbwwsSAJ1q/HM\nSEs8P8amrMrkjkn+trp3/GQ/uzFy7He6WwmJOG78f3QWs+tzu<PERSON>aysoK5nu+/78U\nwkBHc4nJEYsyazanSqNmUpsEzwnrEYejwCUUl3dBJlKlqnZvSsGNSoYUT7xv3U8l\nuN9Y9HMsQHj5jFAOeqFPVMAbCvjMPIozk7X/dzWoEbp2ZInsUds0HO1FJRsEbuT3\nPxLHHoBnTKVzyf1iOa6zEui8Z6siQu4KKzq9PTK/sIQ10zEF1wwryO7aBIQoBgkW\nsgO/dIskBcZcHAWRZ0CXKp0N10JS265GvI7tET593LgNXK50Wb1rIvYKozp3x/b6\n8iGOK08RAgMBAAECggEAJQh49nQHxsinSYGxh91bauq+3CE0YXir8UDALb0WUzjN\nS0UJNRZy5cnSVsp8FcYXlFd63hc40OC2MoOLB2gMaA/H1OwsVp2aiv/fmyWKg812\ndvlyLSEI0jTZ16DbrfNUg7aJSRrNOUyTyLozyDZg7Hk+MQCh+CJZsBnkov230k1F\ndg9xs28qh8ELbaLGqfHfDTKMbhC6URaY7hvKu7qd19bcktzTwvXiXaQu20ks+QXm\nX56sy4+7MHBbW0Hx5gNKW0/FRsO0z6pEDU8WoaNbxEoqDvliXQZ9G3zhZytGKy1T\nEpTUN2fsAK5lqUk2Py67M1Lta/HTmydBdeTkjZ2pPwKBgQDuXeZSqF1GZ5dydTXa\n2Umyve5qbaSdBHcstnVBt7XxJVRIkJUif73bNLYS9K3EBJt7MxFjhgPq4oc7eY/2\n4KbVQDFc/H9rOthcFIe1YS20ZUWTF1+mZ9bzSwvgSTquIECd4VcLZERA7k2Ex47Z\nsrCmnsal8kM5vGA6on+mvlThqwKBgQDiACm23gYgECekBq63ULm3ez19n4faVD+i\nkuulrV8ApeSZWysHytdsM/W4/gz6O8QeBzAd0Q3TeWvr8+QGLHyYNxFFCEgC/92t\nBJTfF/huzt3kHR3qpTYBO0BPwuYKdc4N5q440+u2SVUxqM1MMlcN8NZUwl7Kwy1N\nOlo1l8EOMwKBgAMji2flPa0yqgTg1D+4gsbux1fxb6x9pZAaKvsdjzDIvA5kkQIu\nSnK7g/ZF8I/XO7DooD7+QcjzCLLYy/OFS80dFklSLd9cnALCZnMV/4MTnlHsid7o\nGSWFBHgEntgyJoetsva8cEWmnbC07AWj272l85m10frNpzq+bw3kDbwXAoGASawr\n7QTrkkAx6/KRugiCwiFWpkD/FFkqdWDXmKavtZJetu9VU7D4ELnPfIK00E3zXKaT\nNOUVUL7gQ4a56plN9IDfDFXqw6+NH/3CWgeCYPzmsJodSoSY2kXqbRXnz7kfdsCg\nQr4ytLWrJAkXA6PVZSIBfJfwNsDxX5+KELZrpKkCgYB01vurHkyjTuCpFPlQKwl7\nV2KfuTMdMkQZAr9f/1LkA5SAdyu6ZcyII6CF36bMEf7X8fcDMQq1J7vOZ/mUhGRw\nSJNFFRSj//o+3Unf8cvGj5nXnS8fs5VVmSuUhonzGqhN2f9cleGAeUEXMgKhOXRv\nmMHpOPGccnjKOto2fJjGEg==\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "106385843481054824946", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40baam-7e29b.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}